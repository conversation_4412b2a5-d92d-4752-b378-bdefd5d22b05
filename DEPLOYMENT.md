# Deployment Guide

This guide will help you deploy your AI Chatbot application with the backend on Railway and frontend on Vercel.

## Prerequisites

1. **Railway Account**: Sign up at [railway.app](https://railway.app)
2. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
3. **GitHub Repository**: Your code should be in a GitHub repository
4. **OpenAI API Key**: Get one from [OpenAI Platform](https://platform.openai.com)
5. **Supabase Project**: Set up at [supabase.com](https://supabase.com)

## Backend Deployment (Railway)

### Step 1: Deploy to Railway

1. **Connect Repository**:
   - Go to [railway.app](https://railway.app)
   - Click "Start a New Project"
   - Select "Deploy from GitHub repo"
   - Choose your repository
   - Select the `backend` folder as the root directory

2. **Configure Build Settings**:
   - Railway will automatically detect your Node.js app
   - The `railway.json` file will configure the deployment
   - Build command: `npm install`
   - Start command: `npm start`

### Step 2: Set Environment Variables

In your Railway project dashboard, go to the "Variables" tab and add:

```
OPENAI_API_KEY=your_openai_api_key_here
PORT=5000
FRONTEND_URL=https://your-frontend-domain.vercel.app
```

### Step 3: Get Your Backend URL

After deployment, Railway will provide you with a URL like:
`https://your-app-name.railway.app`

## Frontend Deployment (Vercel)

### Step 1: Deploy to Vercel

1. **Connect Repository**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Set the root directory to `frontend`

2. **Configure Build Settings**:
   - Framework Preset: Vite
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

### Step 2: Set Environment Variables

In your Vercel project dashboard, go to "Settings" > "Environment Variables" and add:

```
VITE_API_URL=https://your-backend-domain.railway.app
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### Step 3: Update Backend CORS

After getting your Vercel URL, update the `FRONTEND_URL` environment variable in Railway:

```
FRONTEND_URL=https://your-frontend-domain.vercel.app
```

## Post-Deployment Steps

### 1. Test the Deployment

1. Visit your Vercel frontend URL
2. Try creating an account and logging in
3. Test the chat functionality
4. Test file upload features

### 2. Update Supabase Settings

In your Supabase dashboard:
1. Go to Authentication > URL Configuration
2. Add your Vercel domain to "Site URL"
3. Add your Vercel domain to "Redirect URLs"

### 3. Monitor Logs

- **Railway**: Check logs in the Railway dashboard
- **Vercel**: Check function logs in the Vercel dashboard

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure `FRONTEND_URL` is set correctly in Railway
2. **API Connection Issues**: Verify `VITE_API_URL` points to your Railway backend
3. **Build Failures**: Check that all dependencies are in `package.json`
4. **Environment Variables**: Ensure all required variables are set in both platforms

### Useful Commands

```bash
# Test backend locally
cd backend
npm start

# Test frontend locally
cd frontend
npm run dev

# Build frontend locally
cd frontend
npm run build
```

## Security Notes

- Never commit `.env` files to your repository
- Use environment variables for all sensitive data
- Regularly rotate your API keys
- Monitor your usage on OpenAI and Supabase dashboards

## Support

If you encounter issues:
1. Check the deployment logs on Railway and Vercel
2. Verify all environment variables are set correctly
3. Test the application locally first
4. Check the browser console for frontend errors
